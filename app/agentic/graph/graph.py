from typing import Annotated, Any
from uuid import UUID

from langchain_core.runnables import Runnable
from langchain_core.tools import StructuredTool, tool
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import InjectedState, create_react_agent
from langgraph.types import Command, interrupt

from app.agentic.context.tools import get_tools
from app.agentic.graph.nodes import fetch_account_node, get_llm
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.workspace.integrations.user_integrations import UserIntegrations


class GraphFactory:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations

    def create_graph(self) -> StateGraph:
        crm_agent = self._create_crm_agent()
        sales_document_agent = self._create_sales_document_agent()
        validation_agent = self._create_validation_agent()
        supervisor_agent = self._create_supervisor_agent(
            "crm_agent", "sales_document_agent", "validation_agent"
        )

        graph = StateGraph(ConversationState)

        graph.add_node("fetch_account", fetch_account_node)
        graph.add_node("supervisor", supervisor_agent)
        graph.add_node("crm_agent", crm_agent)
        graph.add_node("sales_document_agent", sales_document_agent)
        graph.add_node("validation_agent", validation_agent)

        graph.add_conditional_edges(
            START, self._should_fetch_account, ["fetch_account", "supervisor"]
        )
        graph.add_edge("fetch_account", "supervisor")
        graph.add_edge("crm_agent", "supervisor")
        graph.add_edge("sales_document_agent", "supervisor")
        graph.add_edge("validation_agent", "supervisor")

        return graph

    def _create_supervisor_agent(
        self,
        crm_agent_name: str,
        sales_document_agent_name: str,
        validation_agent_name: str,
    ) -> Runnable:
        handoff_to_crm = self.create_handoff_tool(
            crm_agent_name, "Assign a CRM or account-related task."
        )
        handoff_to_docs = self.create_handoff_tool(
            sales_document_agent_name, "Assign a sales document creation task."
        )
        handoff_to_validation = self.create_handoff_tool(
            validation_agent_name, "Assign a validation task for CRM updates."
        )

        return create_react_agent(
            model=get_llm(),
            tools=[handoff_to_crm, handoff_to_docs, handoff_to_validation],
            prompt=AgentPrompts.get_supervisor_system_prompt(),
            name="supervisor",
        )

    def _create_crm_agent(self) -> Runnable:
        crm_tools = self.get_langchain_tools()
        return create_react_agent(
            model=get_llm(),
            tools=crm_tools,
            prompt=AgentPrompts.get_crm_agent_prompt(),
            name="crm_agent",
        )

    def _create_sales_document_agent(self) -> Runnable:
        return create_react_agent(
            model=get_llm(),
            tools=[],
            prompt=AgentPrompts.get_sales_document_agent_prompt(),
            name="sales_document_agent",
        )

    def _create_validation_agent(self) -> Runnable:
        validation_tool = self._create_user_validation_tool()
        return create_react_agent(
            model=get_llm(),
            tools=[validation_tool],
            prompt=AgentPrompts.get_validation_agent_prompt(),
            name="validation_agent",
        )

    def get_langchain_tools(self) -> list[StructuredTool]:
        raw_tools = get_tools(self.user_id, self.user_integrations)
        langchain_tools = [
            StructuredTool(
                name=td.name,
                description=td.description,
                func=None,
                coroutine=td.coroutine,
                args_schema=td.args_schema,
            )
            for td in raw_tools
        ]
        return langchain_tools

    @staticmethod
    def create_handoff_tool(agent_name: str, description: str) -> tool:
        @tool(f"transfer_to_{agent_name}", description=description)
        def handoff(
            state: Annotated[ConversationState, InjectedState],
        ) -> Command:
            return Command(
                goto=agent_name,
                update=state,
                graph=Command.PARENT,
            )

        return handoff

    @staticmethod
    def _should_fetch_account(state: ConversationState) -> str:
        if not state.get("last_refetch_at"):
            return "fetch_account"

        if not state.get("account_info"):
            return "fetch_account"

        return "supervisor"

    def _create_user_validation_tool(self) -> StructuredTool:
        def user_validation_wrapper(update_details: dict[str, Any]) -> str:
            return self._user_validation_logic(update_details)

        return StructuredTool.from_function(
            func=user_validation_wrapper,
            name="request_user_validation",
            description="Request user validation for CRM updates before executing them.",
        )

    def _user_validation_logic(self, update_details: dict[str, Any]) -> str:
        try:
            decision = interrupt(
                {
                    "question": "Do these updates in your CRM look correct?",
                    "update_to_be_made": update_details,
                }
            )

            if decision == "approve":
                return "APPROVED: User has approved the CRM update."
            else:
                return "REJECTED: User has rejected the proposed CRM update. Please revise your approach or ask for more information."
        except Exception as e:
            return f"ERROR: Could not get user validation: {str(e)}"
