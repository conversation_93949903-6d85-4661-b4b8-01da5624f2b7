from datetime import UTC, datetime

from langchain_google_genai import Chat<PERSON>oogleGenerativeAI
from pydantic import SecretStr

from app.agentic.context.account import get_account_details
from app.agentic.graph.state import ConversationState
from app.core.config import config
from app.workspace.integrations.user_integrations import UserIntegrations


def get_llm() -> ChatGoogleGenerativeAI:
    return ChatGoogleGenerativeAI(
        model="gemini-2.5-flash-preview-04-17",
        api_key=SecretStr(config.gemini_api_key),
    )


def fetch_account_node(
    state: ConversationState,
    user_integrations: UserIntegrations,
) -> ConversationState:
    account_details = get_account_details(
        state["user_id"],
        state["crm_account_id"],
        user_integrations,
    )

    state["account_info"] = account_details
    state["last_refetch_at"] = datetime.now(UTC)

    return state
